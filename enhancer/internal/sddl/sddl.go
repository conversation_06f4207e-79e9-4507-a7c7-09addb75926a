package sddl

import (
	"regexp"
	"strings"

	"github.com/precize/common"
	"github.com/precize/elastic"
	emailutils "github.com/precize/enhancer/internal/email"
	"github.com/precize/enhancer/rcontext"
)

func GetSDDLStatus(tenantID string) (sddlStatus bool, err error) {
	emailFormatsQuery := `{"query":{"bool":{"filter":[{"match":{"tenantId.keyword":"` + tenantID + `"}}]}}}`

	companyMetaDocs, err := elastic.ExecuteSearchQuery([]string{elastic.COMPANY_METADATA_INDEX}, emailFormatsQuery)
	if err != nil {
		return
	}

	for _, companyMetaDoc := range companyMetaDocs {

		if isSDDL, ok := companyMetaDoc["isSDDLFollowed"].(bool); ok {
			return isSDDL, nil
		}
	}

	return
}

func HandleIdentitiesWithSDDL(identityID, identityName *string,
	resourceContext *rcontext.ResourceContext) (isSddl bool) {

	if !resourceContext.SDDLStatus {
		return
	}

	uncleanedEmail := *identityID

	derivedEmailWithoutSddl := RemoveSddlSyntaxFromEmail(*identityID)

	if doesNameHaveSddlSyntax(*identityName) || (derivedEmailWithoutSddl != *identityID) {
		isSddl = true
		if _, err := common.ParseAddress(*identityName); err == nil {
			*identityName = common.GetFormattedNameFromEmail(*identityName)
		} else {
			// First Remove SDDL from email
			*identityName = RemoveSddlFromName(*identityName)
			if _, err = common.ParseAddress(derivedEmailWithoutSddl); err == nil {

				isValidEmail := false
				ok := false

				if isValidEmail, ok = resourceContext.GetEmailStatus(derivedEmailWithoutSddl); !ok {
					emailNameMap := map[string]string{
						derivedEmailWithoutSddl: *identityName,
					}
					if emailStatusMap, err := emailutils.CheckEmailValidity(emailNameMap, true, resourceContext); err == nil {
						if isValidEmail, ok = emailStatusMap[derivedEmailWithoutSddl]; ok {
							resourceContext.SetEmailStatus(derivedEmailWithoutSddl, isValidEmail)
							if addr, err := common.ParseAddress(derivedEmailWithoutSddl); err == nil {
								resourceContext.SetEmailStatus(derivedEmailWithoutSddl, isValidEmail)

								if ok := resourceContext.SetChildPrimaryEmail(uncleanedEmail, addr.Address, true); ok {
									*identityID = addr.Address
								}
							}
						}
					}
				} else {
					// IdentityId already present in emailStatusCache
					resourceContext.SetEmailStatus(derivedEmailWithoutSddl, isValidEmail)

					if ok := resourceContext.SetChildPrimaryEmail(uncleanedEmail, derivedEmailWithoutSddl, true); ok {
						*identityID = derivedEmailWithoutSddl
					}
				}

				if !ok {
					//Remove sddl from name
					derivedEmail := emailutils.DeriveEmailFromName(*identityName, resourceContext)
					if addr, err := common.ParseAddress(derivedEmail); err == nil {

						if resourceContext.SetChildPrimaryEmail(uncleanedEmail, addr.Address, true); ok {
							*identityID = addr.Address
						}
					}
				}
			}
		}
	}

	return isSddl
}

func RemoveSddlFromName(name string) string {
	name = strings.ToLower(name)

	name = regexToRemoveSddlSyntaxFromName(name)
	if doesNameHaveSddlSyntax(name) {
		name = regexToRemoveSddlSyntaxFromName(name)
	}

	return common.ConvertToTitleCase(name)
}

func RemoveSddlSyntaxFromEmail(email string) string {
	validEmail := strings.Index(email, "@")
	if validEmail == -1 {
		// returning email so that sddl check does not pass later
		return email
	}

	emailPrefix := email[:validEmail]
	emailDomain := email[validEmail:]

	sddlPattern := strings.Join(sddlEmailKeywords(), "|")

	prefixRegex := regexp.MustCompile(`(?i)^(` + sddlPattern + `)[._+\-]`)
	suffixRegex := regexp.MustCompile(`(?i)[._+\-](` + sddlPattern + `)$`)

	emailPrefix = prefixRegex.ReplaceAllString(emailPrefix, "")
	emailPrefix = suffixRegex.ReplaceAllString(emailPrefix, "")

	return emailPrefix + emailDomain
}

func sddlEmailKeywords() []string {
	keywords := make([]string, 0, len(sddlEmail))
	for key := range sddlEmail {
		keywords = append(keywords, strings.ToLower(key))
	}
	return keywords
}

func regexToRemoveSddlSyntaxFromName(name string) string {
	modifiedName := name

	regexSpecialChars := regexp.MustCompile(`[^a-zA-Z0-9]+`)
	modifiedName = regexSpecialChars.ReplaceAllString(modifiedName, " ")
	modifiedName = strings.TrimSpace(modifiedName)
	nameAfterRemovingSpecialChars := modifiedName

	regexPattern := `\b(` + strings.Join(sddlKeywords(), "|") + `)\b`

	regex := regexp.MustCompile(regexPattern)
	modifiedName = regex.ReplaceAllString(modifiedName, "")

	modifiedName = strings.TrimSpace(modifiedName)

	// if name contains special chars and no sddl prefixes return original name
	if nameAfterRemovingSpecialChars == modifiedName {
		return name
	}
	return modifiedName
}

func sddlKeywords() []string {
	keywords := make([]string, 0, len(sddlSyntax))
	for key := range sddlSyntax {
		keywords = append(keywords, strings.ToLower(key))
	}
	return keywords
}

func doesNameHaveSddlSyntax(name string) bool {
	name = strings.Trim(name, " ")
	name = strings.ToLower(name)

	return name != regexToRemoveSddlSyntaxFromName(name)
}
