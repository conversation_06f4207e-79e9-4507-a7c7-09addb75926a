package controller

import (
	"net/http"

	"github.com/precize/pserver/server/weaviate/model"
	"github.com/precize/pserver/server/weaviate/utils"
)

func (c *WeaviateController) CreateClass(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		utils.WriteError(w, http.StatusMethodNotAllowed, "Method not allowed")
		return
	}

	var req model.CreateClassRequest
	if err := utils.ParseJSON(r, &req); err != nil {
		utils.WriteError(w, http.StatusBadRequest, "Invalid JSON format")
		return
	}

	if req.ClassName == "" {
		utils.WriteError(w, http.StatusBadRequest, "className is required")
		return
	}

	if len(req.VectorConfig) > 1 && len(req.Properties) == 0 {
		utils.WriteError(w, http.StatusBadRequest, "If vector configs are provided, properties are required")
		return
	}

	response, err := c.service.CreateClass(r.Context(), req)
	if err != nil {
		utils.WriteError(w, http.StatusInternalServerError, err.Error())
		return
	}

	utils.WriteResponse(w, http.StatusOK, response)
}

func (c *WeaviateController) DeleteClass(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		utils.WriteError(w, http.StatusMethodNotAllowed, "Method not allowed")
		return
	}

	var req model.DeleteClassRequest
	if err := utils.ParseJSON(r, &req); err != nil {
		utils.WriteError(w, http.StatusBadRequest, "Invalid JSON format")
		return
	}

	if req.ClassName == "" {
		utils.WriteError(w, http.StatusBadRequest, "className is required")
		return
	}

	response, err := c.service.DeleteClass(r.Context(), req)
	if err != nil {
		if utils.IsNotFoundError(err) {
			utils.WriteResponse(w, http.StatusOK, model.Response{
				Success: true,
				Message: "Class does not exist, deletion skipped",
			})
			return
		}
		utils.WriteError(w, http.StatusInternalServerError, err.Error())
		return
	}

	utils.WriteResponse(w, http.StatusOK, response)
}

func (c *WeaviateController) GetClass(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		utils.WriteError(w, http.StatusMethodNotAllowed, "Method not allowed")
		return
	}

	var req model.GetClassRequest
	if err := utils.ParseJSON(r, &req); err != nil {
		utils.WriteError(w, http.StatusBadRequest, "Invalid JSON format")
		return
	}

	if req.ClassName == "" {
		utils.WriteError(w, http.StatusBadRequest, "className is required")
		return
	}

	response, err := c.service.GetClass(r.Context(), req)
	if err != nil {
		if utils.IsNotFoundError(err) {
			utils.WriteError(w, http.StatusNotFound, "Class not found")
			return
		}
		utils.WriteError(w, http.StatusInternalServerError, err.Error())
		return
	}

	utils.WriteResponse(w, http.StatusOK, response)
}
