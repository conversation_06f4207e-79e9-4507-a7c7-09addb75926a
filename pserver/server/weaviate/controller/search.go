package controller

import (
	"net/http"

	"github.com/precize/pserver/server/weaviate/model"
	"github.com/precize/pserver/server/weaviate/utils"
)

func (c *WeaviateController) SearchSimilar(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		utils.WriteError(w, http.StatusMethodNotAllowed, "Method not allowed")
		return
	}

	var req model.SearchSimilarRequest
	if err := utils.ParseJSON(r, &req); err != nil {
		utils.WriteError(w, http.StatusBadRequest, "Invalid JSON format")
		return
	}

	if req.ClassName == "" || req.Query == "" || len(req.Fields) == 0 {
		utils.WriteError(w, http.StatusBadRequest, "className, query, and fields are required")
		return
	}

	response, err := c.service.SearchSimilar(r.Context(), req)
	if err != nil {
		utils.WriteError(w, http.StatusInternalServerError, err.Error())
		return
	}

	utils.WriteResponse(w, http.StatusOK, response)
}

func (c *WeaviateController) SearchAll(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		utils.WriteError(w, http.StatusMethodNotAllowed, "Method not allowed")
		return
	}

	var req model.SearchAllRequest
	if err := utils.ParseJSON(r, &req); err != nil {
		utils.WriteError(w, http.StatusBadRequest, "Invalid JSON format")
		return
	}

	if req.ClassName == "" || len(req.Fields) == 0 {
		utils.WriteError(w, http.StatusBadRequest, "className and fields are required")
		return
	}

	response, err := c.service.SearchAll(r.Context(), req)
	if err != nil {
		utils.WriteError(w, http.StatusInternalServerError, err.Error())
		return
	}

	utils.WriteResponse(w, http.StatusOK, response)
}

func (c *WeaviateController) SearchByID(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		utils.WriteError(w, http.StatusMethodNotAllowed, "Method not allowed")
		return
	}

	var req model.SearchByIDRequest
	if err := utils.ParseJSON(r, &req); err != nil {
		utils.WriteError(w, http.StatusBadRequest, "Invalid JSON format")
		return
	}

	if req.ClassName == "" || req.ID == "" {
		utils.WriteError(w, http.StatusBadRequest, "className and id are required")
		return
	}

	response, err := c.service.SearchByID(r.Context(), req)
	if err != nil {
		if utils.IsNotFoundError(err) {
			utils.WriteError(w, http.StatusNotFound, "Object not found")
			return
		}
		utils.WriteError(w, http.StatusInternalServerError, err.Error())
		return
	}

	utils.WriteResponse(w, http.StatusOK, response)
}

func (c *WeaviateController) SearchQuery(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		utils.WriteError(w, http.StatusMethodNotAllowed, "Method not allowed")
		return
	}

	var req model.SearchQueryRequest
	if err := utils.ParseJSON(r, &req); err != nil {
		utils.WriteError(w, http.StatusBadRequest, "Invalid JSON format")
		return
	}

	if req.ClassName == "" || len(req.Fields) == 0 || len(req.Filters) == 0 {
		utils.WriteError(w, http.StatusBadRequest, "className, fields, and filters are required")
		return
	}

	response, err := c.service.SearchQuery(r.Context(), req)
	if err != nil {
		utils.WriteError(w, http.StatusInternalServerError, err.Error())
		return
	}

	utils.WriteResponse(w, http.StatusOK, response)
}
